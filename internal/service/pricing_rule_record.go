package service

import (
	"context"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/zlog"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service/internal/pricing/engine"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_pricing_rule_record_handler.go github.com/MoeGolibrary/moego-svc-offering/internal/service PricingRuleRecordHandler
type PricingRuleRecordHandler interface {
	Upsert(ctx context.Context, pricingRuleDo *do.PricingRuleRecordDO) (int64, error)
	GetById(ctx context.Context, companyId int64, pricingRuleId int64) (*do.PricingRuleRecordDO, error)
	ListByPage(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, int32, error)
	Calculate(ctx context.Context, request *do.CalculateCompositeDO) (*do.CalculateCompositeResultDO, error)
	Delete(ctx context.Context, companyId int64, pricingRuleId int64, deleteBy int64) error
	ListServiceAddressOverrideRules(ctx context.Context, companyId int64, conditions map[int64][]do.AddressOverrideCondition) (map[int64][]do.AddressOverrideRule, error)
}

type pricingRuleRecordHandler struct {
	companyClient               clients.CompanyClient
	serviceAreaClient           clients.ServiceAreaClient
	pricingRuleRecordRepository repository.PricingRuleRecordRepository
}

func NewPricingRuleRecordHandler() PricingRuleRecordHandler {
	return &pricingRuleRecordHandler{
		companyClient:               clients.NewCompanyClient(resource.GetCompanyServiceClient()),
		serviceAreaClient:           clients.NewServiceAreaClient(resource.GetServiceAreaServiceClient()),
		pricingRuleRecordRepository: repository.NewPricingRuleRecordRepository(resource.GetOfferingDB()),
	}
}

func (h *pricingRuleRecordHandler) Upsert(ctx context.Context, pricingRuleRecordDo *do.PricingRuleRecordDO) (int64, error) {
	return h.pricingRuleRecordRepository.Upsert(ctx, pricingRuleRecordDo)
}

func (h *pricingRuleRecordHandler) GetById(ctx context.Context, companyId int64, pricingRuleId int64) (*do.PricingRuleRecordDO, error) {
	return h.pricingRuleRecordRepository.GetById(ctx, companyId, pricingRuleId)
}

func (h *pricingRuleRecordHandler) ListByPage(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, int32, error) {
	count, err := h.pricingRuleRecordRepository.Count(ctx, whereOpt)
	if err != nil || count == 0 {
		return nil, 0, err
	}
	list, err := h.pricingRuleRecordRepository.List(ctx, whereOpt, pagination)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (h *pricingRuleRecordHandler) Calculate(ctx context.Context, request *do.CalculateCompositeDO) (*do.CalculateCompositeResultDO, error) {
	rules := make([]do.PricingRuleRecordDO, len(request.PricingRuleRecordDOS))
	for i, rule := range request.PricingRuleRecordDOS {
		rules[i] = *rule
	}

	pricingEngine := engine.NewPricingEngine(rules, request.ApplyBestOnly, request.ApplySequence)

	results := pricingEngine.CalculatePrice(request.CalculateDOS)

	responseDetails := prepareResponse(results)

	var formula string
	if request.IsPreview && len(request.CalculateDOS) > 0 {
		symbol, _ := h.getCurrencySymbol(ctx, request.CompanyID)
		formula = pricingEngine.CalculateFormula(request.CalculateDOS, symbol)
	}

	return &do.CalculateCompositeResultDO{
		CalculateResultDOS: responseDetails,
		Formula:            &formula,
	}, nil
}

func (h *pricingRuleRecordHandler) Delete(ctx context.Context, companyId int64, pricingRuleRecordId int64, deleteBy int64) error {
	return h.pricingRuleRecordRepository.Delete(ctx, companyId, pricingRuleRecordId, deleteBy)
}

func prepareResponse(matchedPetDetails []*do.CalculateDO) []*do.CalculateResultDO {
	responseDetails := make([]*do.CalculateResultDO, 0)
	for _, detail := range matchedPetDetails {
		if !detail.IsHitPricingRule {
			continue
		}
		responseDetails = append(responseDetails, &do.CalculateResultDO{
			PetId:              detail.PetId,
			ServiceId:          detail.ServiceId,
			AdjustedPrice:      detail.AdjustedPrice,
			UsedPricingRuleIds: lo.Uniq(detail.UsedPricingRuleIds),
			ServiceDate:        detail.ServiceDate,
		})
	}
	return responseDetails
}

func (h *pricingRuleRecordHandler) getCurrencySymbol(ctx context.Context, companyId int64) (string, error) {
	info, err := h.companyClient.GetCompanyPreferenceSetting(ctx, companyId)
	if err != nil {
		zlog.Error(ctx, "failed to get business info", zap.Error(err))
		return "", err
	}
	return info.GetCurrencySymbol(), nil
}

func (h *pricingRuleRecordHandler) ListServiceAddressOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.AddressOverrideCondition) (map[int64][]do.AddressOverrideRule, error) {
	result := make(map[int64][]do.AddressOverrideRule)

	if len(overrideConditions) == 0 {
		return result, nil
	}

	list, err := h.pricingRuleRecordRepository.List(ctx, &models.PricingRuleRecordWhereOpt{
		CompanyID: companyId,
		IsActive:  proto.Bool(true),
		RuleTypes: []offeringModelsV2.RuleType{offeringModelsV2.RuleType_ZONE},
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(1000),
		PageNum:  proto.Int32(1),
	})
	if err != nil {
		return result, err
	}

	// get the first address from conditions
	zipcode := ""
	var coordinate *latlng.LatLng
	for _, cond := range overrideConditions {
		if len(cond) > 0 {
			zipcode = cond[0].Zipcode
			if cond[0].Coordinate != nil {
				coordinate = cond[0].Coordinate
			}
			break
		}
	}

	// service area to zipcode
	areaId, err := h.serviceAreaClient.SearchServiceAreas(ctx, companyId, zipcode, coordinate)
	if err != nil {
		return result, err
	}

	for _, ruleRecord := range list {
		if ruleRecord.RuleConfiguration == nil {
			continue
		}

		for serviceId, conditions := range overrideConditions {
			for _, cond := range conditions {
				// match service
				isGroomingService :=
					cond.ServiceType == offeringModelsV1.ServiceType_SERVICE &&
						cond.ServiceItemType == offeringModelsV1.ServiceItemType_GROOMING &&
						(ruleRecord.AllGroomingApplicable || lo.Contains(ruleRecord.SelectedGroomingServices, serviceId))
				isAddon := cond.ServiceType == offeringModelsV1.ServiceType_ADDON &&
					(ruleRecord.AllAddonApplicable || lo.Contains(ruleRecord.SelectedAddonServices, serviceId))
				if !isGroomingService && !isAddon {
					continue
				}

				// match condition
				for _, group := range ruleRecord.RuleConfiguration.ConditionGroups {
					matchCondition := false
					for _, condition := range group.Conditions {
						if containsZone(condition, zipcode, areaId) {
							matchCondition = true
							break
						}
					}
					if matchCondition {
						result[serviceId] = append(result[serviceId], do.AddressOverrideRule{
							Zipcode:    zipcode,
							Coordinate: coordinate,
							Effect:     group.GetEffect(),
						})
						break
					}
				}
			}
		}

	}

	return result, nil
}

func containsZone(condition *offeringModelsV2.Condition, zipcode string, areaId *int64) bool {
	if condition.GetType() == offeringModelsV2.ConditionType_ZIPCODE {
		return lo.Contains(condition.GetValue().GetStringValues().GetValues(), zipcode)
	} else if condition.GetType() == offeringModelsV2.ConditionType_SERVICE_AREA {
		return areaId != nil && lo.Contains(condition.GetValue().GetNumberValues().GetValues(), *areaId)
	}
	return false
}
