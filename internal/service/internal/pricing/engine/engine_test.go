package engine

import (
	"fmt"
	"testing"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

const (
	symbol = "$"
)

func TestPricingEngine_CalculatePrice_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(100).RoundBank(2), results[0].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 = $100.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

// TestPricingEngine_CalculatePrice_MultiPetRule_EachPet_ThreePets_MixedServices
// 测试多宠物规则：Pet 1 选择 Service 1，Pet 2, 3 选择 Service 2
// 总共3只宠物，service 2 满足MultiPetRule（>=2只宠物），service 1 不满足MultiPetRule（只有1只宠物）
func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet_ThreePets_MixedServices(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(15),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		// Pet 1 选择 Service 1 (单独一只宠物，不满足多宠物规则)
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(120),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Pet 2 选择 Service 2 (与Pet 3一起，满足多宠物规则)
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Pet 3 选择 Service 2 (与Pet 2一起，满足多宠物规则)
		{
			PetId:           3,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 3, len(results))

	// Pet 1 (Service 1): 不满足多宠物规则，价格不变
	assert.Equal(t, decimal.NewFromFloat32(105).RoundBank(2), results[0].AdjustedPrice) // 120 - 15 = 105 (所有宠物都会应用规则)
	assert.True(t, results[0].IsHitPricingRule)

	// Pet 2 (Service 2): 满足多宠物规则，享受折扣
	assert.Equal(t, decimal.NewFromFloat32(85).RoundBank(2), results[1].AdjustedPrice) // 100 - 15 = 85
	assert.True(t, results[1].IsHitPricingRule)

	// Pet 3 (Service 2): 满足多宠物规则，享受折扣
	assert.Equal(t, decimal.NewFromFloat32(85).RoundBank(2), results[2].AdjustedPrice) // 100 - 15 = 85
	assert.True(t, results[2].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $20.00 = $80.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet_DifferentService(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "($100.00 - $10.00) * (1 - 10%) = $81.00", formula)
}

func TestPricingEngine_CalculatePrice_PeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(110).RoundBank(2), results[0].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_WithPeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

// TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_OnlyMultiPetRule
// 测试当 ApplyBestOnly = true 且有多个 serviceId (>2) 时，如果只存在 multiPetRule，allPetDetails 不会被重复更新的问题
func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_OnlyMultiPetRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil) // ApplyBestOnly = true

	details := []*do.CalculateDO{
		// Service 100 - Pet 1
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 100 - Pet 2
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 1
		{
			PetId:           1,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 2
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 300 - Pet 1
		{
			PetId:           1,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 300 - Pet 2
		{
			PetId:           2,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	// 验证结果
	assert.Equal(t, 6, len(results))

	expectedPrices := []decimal.Decimal{
		decimal.NewFromFloat32(180), // Pet 1, Service 300: 200 - 20 = 180
		decimal.NewFromFloat32(180), // Pet 2, Service 300: 200 - 20 = 180
		decimal.NewFromFloat32(130), // Pet 1, Service 200: 150 - 20 = 130
		decimal.NewFromFloat32(130), // Pet 2, Service 200: 150 - 20 = 130
		decimal.NewFromFloat32(80),  // Pet 1, Service 100: 100 - 20 = 80
		decimal.NewFromFloat32(80),  // Pet 2, Service 100: 100 - 20 = 80
	}

	for i, result := range results {
		assert.True(t, expectedPrices[i].Equal(result.AdjustedPrice),
			"Result %d: expected %s, got %s", i, expectedPrices[i].String(), result.AdjustedPrice.String())
		assert.True(t, result.IsHitPricingRule, "Result %d should hit pricing rule", i)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1), "Result %d should use rule ID 1", i)
		assert.True(t, decimal.NewFromFloat32(20).Equal(result.PricingRuleIdPriceMap[1]),
			"Result %d should have 20 discount", i)
	}
}

// TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_MixedRules
// 测试当 ApplyBestOnly = true 且有多个 serviceId 时，同时存在 multiPetRule 和 multiStayRule 的情况
// 验证每个 serviceId 独立选择最优规则，避免重复更新 allPetDetails 的问题
func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_MixedRules(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(15),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(25),
						},
					},
				},
			},
		},
	}

	// ApplyBestOnly = true
	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		// Service 100 - Pet 1, Day 1
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 100 - Pet 1, Day 2 (满足多住宿条件)
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 100 - Pet 2, Day 1
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 100 - Pet 2, Day 2 (满足多住宿条件)
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 1 (单天，不满足多住宿条件)
		{
			PetId:           1,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 2 (单天，不满足多住宿条件)
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	// 验证结果
	assert.Equal(t, 6, len(results))

	expectedResults := []struct {
		price    decimal.Decimal
		ruleId   int64
		discount decimal.Decimal
	}{
		{decimal.NewFromFloat32(135), 1, decimal.NewFromFloat32(15)}, // Pet 1, Service 200: 150 - 15 = 135 (多宠物规则)
		{decimal.NewFromFloat32(135), 1, decimal.NewFromFloat32(15)}, // Pet 2, Service 200: 150 - 15 = 135 (多宠物规则)
		{decimal.NewFromFloat32(75), 2, decimal.NewFromFloat32(25)},  // Pet 1, Service 100, Day 1: 100 - 25 = 75 (多住宿规则)
		{decimal.NewFromFloat32(75), 2, decimal.NewFromFloat32(25)},  // Pet 1, Service 100, Day 2: 100 - 25 = 75 (多住宿规则)
		{decimal.NewFromFloat32(75), 2, decimal.NewFromFloat32(25)},  // Pet 2, Service 100, Day 1: 100 - 25 = 75 (多住宿规则)
		{decimal.NewFromFloat32(75), 2, decimal.NewFromFloat32(25)},  // Pet 2, Service 100, Day 2: 100 - 25 = 75 (多住宿规则)
	}

	for i, result := range results {
		expected := expectedResults[i]
		assert.True(t, expected.price.Equal(result.AdjustedPrice),
			"Result %d: expected price %s, got %s", i, expected.price.String(), result.AdjustedPrice.String())
		assert.True(t, result.IsHitPricingRule, "Result %d should hit pricing rule", i)
		assert.Contains(t, result.UsedPricingRuleIds, expected.ruleId, "Result %d should use rule %d", i, expected.ruleId)
		assert.True(t, expected.discount.Equal(result.PricingRuleIdPriceMap[expected.ruleId]),
			"Result %d should have %s discount from rule %d", i, expected.discount.String(), expected.ruleId)
	}
}

// TestPricingEngine_CalculatePrice_ApplyBestOnly_NoDoubleUpdate
// 测试确保在 ApplyBestOnly = true 时，allPetDetails 不会被重复更新
// 验证行为：即使有多个 serviceId，多宠物规则也只应用一次
func TestPricingEngine_CalculatePrice_ApplyBestOnly_NoDoubleUpdate(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	// 测试 ApplyBestOnly = true 的情况
	engineBestOnly := NewPricingEngine(rules, true, nil)

	// 测试 ApplyBestOnly = false 的情况
	engineSequential := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		// Service 100
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200
		{
			PetId:           1,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 300
		{
			PetId:           1,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	detailsBestOnly := cloneTestDetails(details)
	detailsSequential := cloneTestDetails(details)

	resultsBestOnly := engineBestOnly.CalculatePrice(detailsBestOnly)
	resultsSequential := engineSequential.CalculatePrice(detailsSequential)

	assert.Equal(t, len(resultsSequential), len(resultsBestOnly))

	for i := range resultsBestOnly {
		assert.True(t, resultsBestOnly[i].AdjustedPrice.Equal(resultsSequential[i].AdjustedPrice),
			"Result %d: BestOnly price %s should equal Sequential price %s",
			i, resultsBestOnly[i].AdjustedPrice.String(), resultsSequential[i].AdjustedPrice.String())

		assert.Equal(t, resultsBestOnly[i].IsHitPricingRule, resultsSequential[i].IsHitPricingRule,
			"Result %d: IsHitPricingRule should be equal", i)

		assert.Equal(t, resultsBestOnly[i].UsedPricingRuleIds, resultsSequential[i].UsedPricingRuleIds,
			"Result %d: UsedPricingRuleIds should be equal", i)

		expectedPrice := resultsBestOnly[i].ServicePrice.Sub(decimal.NewFromFloat32(10))
		assert.True(t, expectedPrice.Equal(resultsBestOnly[i].AdjustedPrice),
			"Result %d: expected price %s, got %s",
			i, expectedPrice.String(), resultsBestOnly[i].AdjustedPrice.String())
	}
}

// cloneTestDetails 克隆测试数据以避免相互影响
func cloneTestDetails(details []*do.CalculateDO) []*do.CalculateDO {
	cloned := make([]*do.CalculateDO, len(details))
	for i, detail := range details {
		cloned[i] = &do.CalculateDO{
			PetId:           detail.PetId,
			ServiceId:       detail.ServiceId,
			ServicePrice:    detail.ServicePrice,
			LodgingUnitId:   detail.LodgingUnitId,
			ScopeTypePrice:  detail.ScopeTypePrice,
			ServiceDate:     detail.ServiceDate,
			ServiceItemType: detail.ServiceItemType,
		}
	}
	return cloned
}

// 表格驱动测试的另一种写法（更简洁）
func TestSelectBetterCalculateResult_TableDriven(t *testing.T) {
	tests := []struct {
		name         string
		currentPrice string
		currentHit   bool
		bestPrice    string
		bestHit      bool
		expectPrice  string
		expectHit    bool
		currentNil   bool
		bestNil      bool
		expectNil    bool
	}{
		{"both_nil", "", false, "", false, "", false, true, true, true},
		{"current_nil", "", false, "100.00", true, "100.00", true, true, false, false},
		{"current_no_hit", "50.00", false, "100.00", true, "100.00", true, false, false, false},
		{"best_nil_current_hit", "75.00", true, "", false, "75.00", true, false, true, false},
		{"best_higher", "50.00", true, "100.00", true, "100.00", true, false, false, false},
		{"current_higher", "150.00", true, "100.00", true, "150.00", true, false, false, false},
		{"equal_prices", "100.00", true, "100.00", true, "100.00", true, false, false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var current, best *do.CalculateDO

			if !tt.currentNil {
				currentPrice, err := decimal.NewFromString(tt.currentPrice)
				if err != nil {
					return
				}
				current = &do.CalculateDO{
					AdjustedPrice:    currentPrice,
					IsHitPricingRule: tt.currentHit,
				}
			}

			if !tt.bestNil {
				bestPrice, err := decimal.NewFromString(tt.bestPrice)
				if err != nil {
					return
				}
				best = &do.CalculateDO{
					AdjustedPrice:    bestPrice,
					IsHitPricingRule: tt.bestHit,
				}
			}

			result := selectBetterCalculateResult(current, best)

			if tt.expectNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				expectPrice, err := decimal.NewFromString(tt.expectPrice)
				if err != nil {
					return
				}
				assert.True(t, expectPrice.Equal(result.AdjustedPrice))
				assert.Equal(t, tt.expectHit, result.IsHitPricingRule)
			}
		})
	}
}

// TestPricingEngine_ComplexScenario_MultipleRuleTypes_ApplyBestOnly
// 测试复杂场景：ApplyBestOnly模式下，多个服务ID各自有不同最优规则选择
func TestPricingEngine_ComplexScenario_MultipleRuleTypes_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		// 多宠物规则：每只宠物折扣15
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets 15",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(15),
						},
					},
				},
			},
		},
		// 多住宿规则：每天折扣30（更优）
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays 30",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(30),
						},
					},
				},
			},
		},
		// Peak date规则：增加20
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date 20",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-03",
										},
									},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil) // ApplyBestOnly = true

	details := []*do.CalculateDO{
		// Service 100 - 2只宠物，3天住宿，均在peak日期
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},

		// Service 200 - 2只宠物，单天，在peak日期（应该选择多宠物规则）
		{PetId: 1, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},

		// Service 300 - 1只宠物，2天，不在peak日期（只能选择多住宿规则）
		{PetId: 3, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-05"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 3, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-06"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 10, len(results))

	// 验证Service 100选择了多住宿规则（折扣30）+ peak date增加20 = 最终折扣10
	service100Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 100 })
	for _, result := range service100Results {
		expectedPrice := decimal.NewFromFloat32(90) // 100 - 30 + 20 = 90
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice), "Service 100 should be 90, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(2)) // multi stay rule
		assert.Contains(t, result.UsedPricingRuleIds, int64(3)) // peak date rule
	}

	// 验证Service 200选择了多宠物规则（折扣15）+ peak date增加20 = 最终增加5
	service200Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 200 })
	for _, result := range service200Results {
		expectedPrice := decimal.NewFromFloat32(155) // 150 - 15 + 20 = 155
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice), "Service 200 should be 155, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1)) // multi pet rule
		assert.Contains(t, result.UsedPricingRuleIds, int64(3)) // peak date rule
	}

	// 验证Service 300只有多住宿规则生效
	service300Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 300 })
	for _, result := range service300Results {
		expectedPrice := decimal.NewFromFloat32(170) // 200 - 30 = 170
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice), "Service 300 should be 170, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(2))    // multi stay rule only
		assert.NotContains(t, result.UsedPricingRuleIds, int64(3)) // no peak date
	}
}

// TestPricingEngine_ComplexScenario_SequentialRules_CompoundDiscount
// 测试复杂场景：顺序应用规则时的复合折扣计算
func TestPricingEngine_ComplexScenario_SequentialRules_CompoundDiscount(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		// 多宠物规则：固定折扣20
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 20},
					},
				},
			},
		},
		// 多住宿规则：百分比折扣15%
		{
			ID:                    proto.Int64(2),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_STAY,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 3}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT, Value: 15},
					},
				},
			},
		},
		// Peak date规则：固定增加25
		{
			ID:                    proto.Int64(3),
			RuleType:              offeringModelsV2.RuleType_PEAK_DATE,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{StartDate: "2025-03-01", EndDate: "2025-03-05"},
									},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_INCREASE, Value: 25},
					},
				},
			},
		},
	}

	// 按顺序应用：先多宠物，再多住宿，最后peak date
	applySequence := []offeringModelsV2.RuleType{
		offeringModelsV2.RuleType_MULTIPLE_PET,
		offeringModelsV2.RuleType_MULTIPLE_STAY,
	}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		// 2只宠物，3天住宿，在peak日期
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	// 计算期望价格：
	// 1. 原价：200
	// 2. 多宠物规则：200 - 20 = 180
	// 3. 多住宿规则：180 * (1 - 15%) = 180 * 0.85 = 153
	// 4. Peak date规则：153 + 25 = 178
	expectedPrice := decimal.NewFromFloat32(178)

	for _, result := range results {
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice),
			"Expected %s, got %s", expectedPrice.String(), result.AdjustedPrice.String())
		assert.True(t, result.IsHitPricingRule)
		assert.Len(t, result.UsedPricingRuleIds, 3) // 应用了3个规则
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
		assert.Contains(t, result.UsedPricingRuleIds, int64(2))
		assert.Contains(t, result.UsedPricingRuleIds, int64(3))
	}
}

// TestPricingEngine_ComplexScenario_MixedServiceTypes
// 测试复杂场景：混合服务类型（Boarding和Daycare）
func TestPricingEngine_ComplexScenario_MixedServiceTypes(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		// 只适用于Boarding的规则
		{
			ID:                       proto.Int64(1),
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable:    true,
			AllDaycareApplicable:     false,
			SelectedBoardingServices: nil,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 30},
					},
				},
			},
		},
		// 只适用于Daycare的规则
		{
			ID:                       proto.Int64(2),
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable:    false,
			AllDaycareApplicable:     true,
			SelectedBoardingServices: nil,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 20},
					},
				},
			},
		},
		// 指定服务ID的规则
		{
			ID:                       proto.Int64(3),
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			AllBoardingApplicable:    false,
			AllDaycareApplicable:     false,
			SelectedBoardingServices: []int64{100}, // 只适用于服务100
			SelectedDaycareServices:  []int64{200}, // 只适用于服务200
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 25},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil) // ApplyBestOnly = true

	details := []*do.CalculateDO{
		// Boarding服务100 - 2只宠物，2天（适用规则1和3）
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},

		// Daycare服务200 - 2只宠物，2天（适用规则2和3）
		{PetId: 1, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_DAYCARE},
		{PetId: 1, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_DAYCARE},
		{PetId: 2, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_DAYCARE},
		{PetId: 2, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_DAYCARE},

		// Boarding服务300 - 2只宠物，1天（只适用规则1）
		{PetId: 1, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(120), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(120), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	// 验证Boarding服务100：规则1折扣30 vs 规则3折扣25，选择规则1
	service100Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 100 })
	for _, result := range service100Results {
		expectedPrice := decimal.NewFromFloat32(120) // 150 - 30 = 120
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
		assert.NotContains(t, result.UsedPricingRuleIds, int64(3))
	}

	// 验证Daycare服务200：规则2折扣20 vs 规则3折扣25，选择规则3
	service200Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 200 })
	for _, result := range service200Results {
		expectedPrice := decimal.NewFromFloat32(75) // 100 - 25 = 75
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(3))
		assert.NotContains(t, result.UsedPricingRuleIds, int64(2))
	}

	// 验证Boarding服务300：只有规则1适用
	service300Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 300 })
	for _, result := range service300Results {
		expectedPrice := decimal.NewFromFloat32(90) // 120 - 30 = 90
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
		assert.Len(t, result.UsedPricingRuleIds, 1)
	}
}

// TestPricingEngine_ComplexScenario_LargeDataset_Performance
// 测试复杂场景：大数据集性能测试
func TestPricingEngine_ComplexScenario_LargeDataset_Performance(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable: true,
			AllDaycareApplicable:  true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 10},
					},
				},
			},
		},
		{
			ID:                    proto.Int64(2),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_STAY,
			AllBoardingApplicable: true,
			AllDaycareApplicable:  true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 3}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 15},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil) // ApplyBestOnly = true

	// 生成大数据集：50只宠物，10个服务，每个服务7天
	var details []*do.CalculateDO
	for petId := 1; petId <= 50; petId++ {
		for serviceId := 100; serviceId <= 109; serviceId++ {
			for day := 1; day <= 7; day++ {
				details = append(details, &do.CalculateDO{
					PetId:           int64(petId),
					ServiceId:       int64(serviceId),
					ServicePrice:    decimal.NewFromFloat32(100),
					ServiceDate:     lo.ToPtr(fmt.Sprintf("2025-03-%02d", day)),
					ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
				})
			}
		}
	}

	// 验证数据集大小
	assert.Equal(t, 3500, len(details)) // 50 * 10 * 7 = 3500

	results := engine.CalculatePrice(details)

	// 验证结果数量
	assert.Equal(t, len(details), len(results))

	// 验证所有结果都应用了多住宿规则（因为每个宠物都有7天，满足>=3天条件）
	for _, result := range results {
		expectedPrice := decimal.NewFromFloat32(85) // 100 - 15 = 85
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(2)) // multi stay rule
	}
}

// TestPricingEngine_ComplexScenario_EdgeCase_ZeroPrices
// 测试复杂场景：边界情况 - 零价格和价格下限保护
func TestPricingEngine_ComplexScenario_EdgeCase_ZeroPrices(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 50},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		// 零价格服务
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.Zero, ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.Zero, ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		// 低价格服务（折扣后应该限制在0以上）
		{PetId: 1, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(30), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(30), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		// 正好等于折扣金额的价格
		{PetId: 1, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(50), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 300, ServicePrice: decimal.NewFromFloat32(50), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	// 验证零价格服务 - 应该保持为零
	service100Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 100 })
	for _, result := range service100Results {
		assert.True(t, decimal.Zero.Equal(result.AdjustedPrice), "Zero price should remain zero, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
	}

	// 验证低价格服务 - 折扣后不应为负数，最低为0
	service200Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 200 })
	for _, result := range service200Results {
		// 30 - 50 = -20，但应该被限制为0
		assert.True(t, decimal.Zero.Equal(result.AdjustedPrice), "Price should not go below zero, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
	}

	// 验证正好等于折扣金额的价格 - 应该变为0
	service300Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 300 })
	for _, result := range service300Results {
		// 50 - 50 = 0
		assert.True(t, decimal.Zero.Equal(result.AdjustedPrice), "Price should be zero when discount equals original price, got %s", result.AdjustedPrice)
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
	}
}

// TestPricingEngine_ComplexScenario_PeakDate_MultipleRanges
// 测试复杂场景：多个Peak Date规则重叠
func TestPricingEngine_ComplexScenario_PeakDate_MultipleRanges(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		// Peak Date规则1：增加30
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_PEAK_DATE,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{StartDate: "2025-03-01", EndDate: "2025-03-05"},
									},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_INCREASE, Value: 30},
					},
				},
			},
		},
		// Peak Date规则2：增加50（重叠期间）
		{
			ID:                    proto.Int64(2),
			RuleType:              offeringModelsV2.RuleType_PEAK_DATE,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{StartDate: "2025-03-03", EndDate: "2025-03-07"},
									},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_INCREASE, Value: 50},
					},
				},
			},
		},
		// Peak Date规则3：百分比增加20%
		{
			ID:                    proto.Int64(3),
			RuleType:              offeringModelsV2.RuleType_PEAK_DATE,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{StartDate: "2025-03-04", EndDate: "2025-03-06"},
									},
								},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_PERCENTAGE_INCREASE, Value: 20},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, nil)

	details := []*do.CalculateDO{
		// 不同日期的服务
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 只有规则1
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 只有规则1
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 规则1和2
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-04"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 规则1、2、3
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-05"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 规则1、2、3
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-06"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 规则2、3
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-07"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 只有规则2
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-08"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING}, // 无规则
	}

	results := engine.CalculatePrice(details)

	expectedResults := []struct {
		date  string
		price decimal.Decimal
		rules []int64
	}{
		{"2025-03-01", decimal.NewFromFloat32(130), []int64{1}}, // 100 + 30 = 130
		{"2025-03-02", decimal.NewFromFloat32(130), []int64{1}}, // 100 + 30 = 130
		{"2025-03-03", decimal.NewFromFloat32(150), []int64{2}}, // 取最高的50
		{"2025-03-04", decimal.NewFromFloat32(150), []int64{2}}, // 取最高的50（规则3的20%=20 < 50）
		{"2025-03-05", decimal.NewFromFloat32(150), []int64{2}}, // 取最高的50
		{"2025-03-06", decimal.NewFromFloat32(150), []int64{2}}, // 取最高的50
		{"2025-03-07", decimal.NewFromFloat32(150), []int64{2}}, // 100 + 50 = 150
		{"2025-03-08", decimal.NewFromFloat32(100), []int64{}},  // 无规则适用
	}

	for i, result := range results {
		expected := expectedResults[i]
		assert.True(t, expected.price.Equal(result.AdjustedPrice),
			"Date %s: expected %s, got %s", expected.date, expected.price.String(), result.AdjustedPrice.String())

		if len(expected.rules) == 0 {
			assert.False(t, result.IsHitPricingRule)
		} else {
			assert.True(t, result.IsHitPricingRule)
			for _, ruleId := range expected.rules {
				assert.Contains(t, result.UsedPricingRuleIds, ruleId)
			}
		}
	}
}

// TestPricingEngine_ComplexScenario_RuleApplyType_Additional
// 测试复杂场景：APPLY_TO_ADDITIONAL规则类型的复合应用
func TestPricingEngine_ComplexScenario_RuleApplyType_Additional(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		// 多宠物规则：只对额外宠物应用
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT, Value: 50},
					},
				},
			},
		},
		// 多住宿规则：对每天应用
		{
			ID:                    proto.Int64(2),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_STAY,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 3}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 20},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{
		offeringModelsV2.RuleType_MULTIPLE_PET,
		offeringModelsV2.RuleType_MULTIPLE_STAY,
	}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		// 3只宠物，3天住宿，价格递减（确保排序正确）
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(300), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(300), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(300), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 3, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 3, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-02"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 3, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-03"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	// Pet 1（最高价格）：不应用多宠物折扣，但应用多住宿折扣
	pet1Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.PetId == 1 })
	for _, result := range pet1Results {
		expectedPrice := decimal.NewFromFloat32(280) // 300 - 20 = 280
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(2))    // multi stay
		assert.NotContains(t, result.UsedPricingRuleIds, int64(1)) // no multi pet
	}

	// Pet 2（次高价格）：应用多宠物折扣50% + 多住宿折扣
	pet2Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.PetId == 2 })
	for _, result := range pet2Results {
		// 先应用多宠物折扣：200 * 50% = 100
		// 再应用多住宿折扣：100 - 20 = 80
		expectedPrice := decimal.NewFromFloat32(80)
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(1)) // multi pet
		assert.Contains(t, result.UsedPricingRuleIds, int64(2)) // multi stay
	}

	// Pet 3（最低价格）：应用多宠物折扣50% + 多住宿折扣
	pet3Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.PetId == 3 })
	for _, result := range pet3Results {
		// 先应用多宠物折扣：100 * 50% = 50
		// 再应用多住宿折扣：50 - 20 = 30
		expectedPrice := decimal.NewFromFloat32(30)
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.Contains(t, result.UsedPricingRuleIds, int64(1)) // multi pet
		assert.Contains(t, result.UsedPricingRuleIds, int64(2)) // multi stay
	}
}

// TestPricingEngine_ComplexScenario_DataConsistency
// 测试复杂场景：数据一致性验证
func TestPricingEngine_ComplexScenario_DataConsistency(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                    proto.Int64(1),
			RuleType:              offeringModelsV2.RuleType_MULTIPLE_PET,
			AllBoardingApplicable: true,
			RuleApplyType:         offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value:    &offeringModelsV2.GenericValue{Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: 2}},
							},
						},
						Effect: &offeringModelsV2.Effect{Type: offeringModelsV2.EffectType_FIXED_DISCOUNT, Value: 25},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{PetId: 1, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(100), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 2, ServiceId: 100, ServicePrice: decimal.NewFromFloat32(150), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
		{PetId: 3, ServiceId: 200, ServicePrice: decimal.NewFromFloat32(200), ServiceDate: lo.ToPtr("2025-03-01"), ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING},
	}

	results := engine.CalculatePrice(details)

	// 验证基本数据一致性
	assert.Equal(t, len(details), len(results))

	// 创建原始数据的映射以便按PetId和ServiceId查找
	originalMap := make(map[string]*do.CalculateDO)
	for _, detail := range details {
		key := fmt.Sprintf("%d_%d", detail.PetId, detail.ServiceId)
		originalMap[key] = detail
	}

	// 验证每个结果都有正确的基础字段（不依赖顺序）
	for _, result := range results {
		key := fmt.Sprintf("%d_%d", result.PetId, result.ServiceId)
		original, exists := originalMap[key]
		assert.True(t, exists, "Result should have corresponding original data")
		assert.Equal(t, original.PetId, result.PetId)
		assert.Equal(t, original.ServiceId, result.ServiceId)
		assert.Equal(t, original.ServiceDate, result.ServiceDate)
		assert.Equal(t, original.ServiceItemType, result.ServiceItemType)
		assert.True(t, original.ServicePrice.Equal(result.ServicePrice))
	}

	// 验证价格折扣映射的一致性
	for _, result := range results {
		if result.IsHitPricingRule {
			assert.NotEmpty(t, result.UsedPricingRuleIds)
			assert.NotEmpty(t, result.PricingRuleIdPriceMap)

			// 验证UsedPricingRuleIds和PricingRuleIdPriceMap的一致性
			for _, ruleId := range result.UsedPricingRuleIds {
				_, exists := result.PricingRuleIdPriceMap[ruleId]
				assert.True(t, exists, "Rule ID %d should exist in price map", ruleId)
			}
		} else {
			assert.Empty(t, result.UsedPricingRuleIds)
		}
	}

	// 验证折扣计算的正确性 - Service 100有2只宠物
	service100Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 100 })
	for _, result := range service100Results {
		expectedPrice := result.ServicePrice.Sub(decimal.NewFromFloat32(25))
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.True(t, result.IsHitPricingRule)
		assert.True(t, decimal.NewFromFloat32(25).Equal(result.PricingRuleIdPriceMap[1]))
	}

	// Service 200也应该有折扣（全局有3只宠物，满足>=2的条件）
	service200Results := lo.Filter(results, func(r *do.CalculateDO, _ int) bool { return r.ServiceId == 200 })
	for _, result := range service200Results {
		expectedPrice := result.ServicePrice.Sub(decimal.NewFromFloat32(25)) // 200 - 25 = 175
		assert.True(t, expectedPrice.Equal(result.AdjustedPrice))
		assert.True(t, result.IsHitPricingRule)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1))
		assert.True(t, decimal.NewFromFloat32(25).Equal(result.PricingRuleIdPriceMap[1]))
	}
}
