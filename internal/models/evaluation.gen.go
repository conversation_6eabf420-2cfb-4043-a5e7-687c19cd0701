// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

const TableNameEvaluation = "evaluation"

// Evaluation mapped from table <evaluation>
type Evaluation struct {
	ID                      int64                                   `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name                    string                                  `gorm:"column:name;not null" json:"name"`
	CompanyID               int64                                   `gorm:"column:company_id;not null" json:"company_id"`
	AvailableForAllBusiness *bool                                   `gorm:"column:available_for_all_business;default:true" json:"available_for_all_business"`
	AvailableBusinessIDList pq.Int64Array                           `gorm:"column:available_business_id_list;type:bigint[];default:ARRAY[]" json:"available_business_id_list"`
	ServiceItemTypeList     pq.Int32Array                           `gorm:"column:service_item_type_list;type:int[];default:ARRAY[]" json:"service_item_type_list"`
	Price                   *float64                                `gorm:"column:price" json:"price"`
	Duration                *int32                                  `gorm:"column:duration" json:"duration"`
	ColorCode               *string                                 `gorm:"column:color_code;default:#000000" json:"color_code"`
	IsActive                bool                                    `gorm:"column:is_active;not null" json:"is_active"`
	LodgingFilter           bool                                    `gorm:"column:lodging_filter;not null" json:"lodging_filter"`
	AllowedLodgingList      pq.Int64Array                           `gorm:"column:allowed_lodging_list;type:bigint[];not null;default:ARRAY[];comment:allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true" json:"allowed_lodging_list"` // allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
	Description             string                                  `gorm:"column:description;not null" json:"description"`
	OnlineBookingAlias      *string                                 `gorm:"column:online_booking_alias;comment:alias shown in ob flow" json:"online_booking_alias"` // alias shown in ob flow
	IsOnlineBookAvailable   *bool                                   `gorm:"column:is_online_book_available;not null;default:true" json:"is_online_book_available"`
	IsAllStaff              *bool                                   `gorm:"column:is_all_staff;not null;default:true" json:"is_all_staff"`
	AllowedStaffList        pq.Int64Array                           `gorm:"column:allowed_staff_list;type:bigint[];not null;default:ARRAY[];comment:allowed staff id list, only when is_all_staff is true" json:"allowed_staff_list"` // allowed staff id list, only when is_all_staff is true
	AllowStaffAutoAssign    bool                                    `gorm:"column:allow_staff_auto_assign;not null;comment:whether to support automatic allocation of staff" json:"allow_staff_auto_assign"`                          // whether to support automatic allocation of staff
	IsResettable            bool                                    `gorm:"column:is_resettable;not null" json:"is_resettable"`
	ResetIntervalDays       int32                                   `gorm:"column:reset_interval_days;not null" json:"reset_interval_days"`
	BreedFilter             bool                                    `gorm:"column:breed_filter;not null;comment:If true, the evaluation is filtered by pet type and breed" json:"breed_filter"` // If true, the evaluation is filtered by pet type and breed
	TaxID                   int64                                   `gorm:"column:tax_id;not null" json:"tax_id"`
	CreatedAt               *time.Time                              `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt               *time.Time                              `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt               gorm.DeletedAt                          `gorm:"column:deleted_at" json:"deleted_at"`
	Source                  offeringModelsV1.EvaluationModel_Source `gorm:"column:source;not null;default:1;comment:1-MoeGo Platform 2-Enterprise Hub" json:"source"` // 1-MoeGo Platform 2-Enterprise Hub
}

// TableName Evaluation's table name
func (*Evaluation) TableName() string {
	return TableNameEvaluation
}
